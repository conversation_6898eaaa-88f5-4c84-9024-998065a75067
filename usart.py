'''
实验名称：串口通信
版本： v1.0
日期： 2022.9
作者： 01Studio
说明：通过编程实现串口通信，跟电脑串口助手实现数据收发。
'''

from machine import UART,Timer
from fpioa_manager import fm
import time

#映射串口引脚
fm.register(6, fm.fpioa.UART1_RX, force=True)
fm.register(7, fm.fpioa.UART1_TX, force=True)

#初始化串口
uart = UART(UART.UART1, 115200,stop=1, read_buf_len=4096)
uart.write('Hello 01Studio!')
x=123
y=456
while True:
    x=x+1
    if x>=900 :
        x=1
    print(x)
    #buf=int(x)*100+int(y);
    #uart.write(str(x)) #数据回传
    xb=int(x/100)%10;
    xs=int(x/10)%10;
    xg=int(x%10);
    yb=int(y/100)%10;
    ys=int(y/10)%10;
    yg=int(y%10);
    print(xg)

    uart.write('#') #数据回传
    time.sleep(0.002)
    uart.write('*') #数据回传
    time.sleep(0.002)
    uart.write(str(xb)) #数据回传
    time.sleep(0.002)
    uart.write(str(xs)) #数据回传
    time.sleep(0.002)
    uart.write(str(xg)) #数据回传
    time.sleep(0.002)
    uart.write(str(yb)) #数据回传
    time.sleep(0.002)
    uart.write(str(ys)) #数据回传
    time.sleep(0.002)
    uart.write(str(yg)) #数据回传
    time.sleep(0.002)
    uart.write('*') #数据回传
    time.sleep(0.02)
    uart.write('&') #数据回传




